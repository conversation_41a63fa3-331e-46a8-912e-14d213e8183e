import { useState } from "react";
import { Edit<PERSON><PERSON>, TrashIcon, MoreHorizontalIcon } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { trpcClient } from "@/utils/trpc";
import { EditVariantDialog } from "./dialog/edit-variant-dialog";

interface Variant {
  id: string;
  name: string;
  description?: string | null;
  price: string | number;
  stock: Array<{
    id: string;
    status: "AVAILABLE" | "ASSIGNED" | "SOLD";
  }>;
  createdAt: string | Date;
}

interface VariantsTableProps {
  productId: string;
  variants: Variant[];
  onUpdate?: () => void;
}

export function VariantsTable({
  productId,
  variants,
  onUpdate,
}: VariantsTableProps) {
  const [editingVariant, setEditingVariant] = useState<Variant | null>(null);
  const [deletingVariant, setDeletingVariant] = useState<Variant | null>(null);

  const deleteVariantMutation = useMutation({
    mutationFn: async (data: { id: string }) => {
      return trpcClient.variant.delete.mutate({
        id: data.id,
      });
    },
    onSuccess: () => {
      toast.success("Variant deleted successfully!");
      setDeletingVariant(null);
      if (onUpdate) {
        onUpdate();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete variant");
    },
  });

  const handleDeleteVariant = async (variant: Variant) => {
    deleteVariantMutation.mutate({ id: variant.id });
  };

  if (variants.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No variants found</p>
        <p className="text-sm text-muted-foreground mt-1">
          Create your first variant to get started
        </p>
      </div>
    );
  }

  return (
    <>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="w-[70px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {variants.map((variant) => {
            const totalStock = variant.stock.length;
            const availableStock = variant.stock.filter(
              (s) => s.status === "AVAILABLE"
            ).length;

            return (
              <TableRow key={variant.id}>
                <TableCell className="font-medium">{variant.name}</TableCell>
                <TableCell>
                  <div className="max-w-[200px] truncate">
                    {variant.description || "—"}
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-mono">
                    ${Number(variant.price).toLocaleString()}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={availableStock > 0 ? "default" : "secondary"}
                    >
                      {availableStock}/{totalStock}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      available
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-sm">
                    {new Date(variant.createdAt).toLocaleDateString()}
                  </span>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontalIcon className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => setEditingVariant(variant)}
                      >
                        <EditIcon className="h-4 w-4 mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => setDeletingVariant(variant)}
                        className="text-destructive focus:text-destructive"
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      {/* Edit Variant Dialog */}
      {editingVariant && (
        <EditVariantDialog
          variant={editingVariant}
          open={!!editingVariant}
          onOpenChange={(open) => !open && setEditingVariant(null)}
          onSuccess={() => {
            setEditingVariant(null);
            if (onUpdate) {
              onUpdate();
            }
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deletingVariant}
        onOpenChange={(open) => !open && setDeletingVariant(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Variant</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the variant "
              {deletingVariant?.name}"? This action cannot be undone.
              {deletingVariant?.stock && deletingVariant.stock.length > 0 && (
                <span className="block mt-2 text-destructive font-medium">
                  Warning: This variant has {deletingVariant.stock.length} stock
                  items. You may not be able to delete it if there are existing
                  orders.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                deletingVariant && handleDeleteVariant(deletingVariant)
              }
              disabled={deleteVariantMutation.isPending}
            >
              {deleteVariantMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
