import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { trpcClient } from "@/utils/trpc";

// Validation schema
const createVariantSchema = z.object({
  name: z.string().min(1, "Variant name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
});

type CreateVariantFormData = z.infer<typeof createVariantSchema>;

interface CreateVariantDialogProps {
  productId: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateVariantDialog({
  productId,
  open,
  onOpenChange,
  onSuccess,
}: CreateVariantDialogProps) {
  const createVariantMutation = useMutation({
    mutationFn: async (data: CreateVariantFormData & { productId: string }) => {
      return trpcClient.variant.create.mutate({
        name: data.name,
        description: data.description,
        price: data.price,
        productId: data.productId,
      });
    },
    onSuccess: () => {
      toast.success("Variant created successfully!");
      onOpenChange(false);
      form.reset();

      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to create variant");
    },
  });

  const form = useForm({
    defaultValues: {
      name: "",
      description: "",
      price: 0,
    } as CreateVariantFormData,
    onSubmit: async ({ value }) => {
      createVariantMutation.mutate({
        ...value,
        productId,
      });
    },
    validators: {
      onSubmit: createVariantSchema,
    },
  });

  // Reset form when dialog opens/closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Variant</DialogTitle>
          <DialogDescription>
            Add a new variant to this product with its own pricing and details.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Variant Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., 30 hari full garansi"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="price">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Price</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.handleChange(value ? parseFloat(value) : 0);
                    }}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="description">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Description (Optional)</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    placeholder="Describe this variant..."
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    rows={3}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  disabled={
                    !state.canSubmit ||
                    state.isSubmitting ||
                    createVariantMutation.isPending
                  }
                >
                  {state.isSubmitting || createVariantMutation.isPending
                    ? "Creating..."
                    : "Create Variant"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
