import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { trpcClient } from "@/utils/trpc";

// Validation schema
const updateVariantSchema = z.object({
  name: z.string().min(1, "Variant name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be positive"),
});

type UpdateVariantFormData = z.infer<typeof updateVariantSchema>;

interface EditVariantDialogProps {
  variant: {
    id: string;
    name: string;
    description?: string | null;
    price: string | number;
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditVariantDialog({
  variant,
  open,
  onOpenChange,
  onSuccess,
}: EditVariantDialogProps) {
  const updateVariantMutation = useMutation({
    mutationFn: async (data: UpdateVariantFormData & { id: string }) => {
      return trpcClient.variant.update.mutate({
        id: data.id,
        name: data.name,
        description: data.description,
        price: data.price,
      });
    },
    onSuccess: () => {
      toast.success("Variant updated successfully!");
      onOpenChange(false);
      form.reset();

      if (onSuccess) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to update variant");
    },
  });

  const form = useForm({
    defaultValues: {
      name: variant.name,
      description: variant.description || "",
      price: Number(variant.price),
    } as UpdateVariantFormData,
    onSubmit: async ({ value }) => {
      updateVariantMutation.mutate({
        id: variant.id,
        ...value,
      });
    },
    validators: {
      onSubmit: updateVariantSchema,
    },
  });

  // Reset form when dialog opens/closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Variant</DialogTitle>
          <DialogDescription>
            Update the variant information. Changes will be saved immediately.
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            void form.handleSubmit();
          }}
          className="space-y-4"
        >
          <div>
            <form.Field name="name">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Variant Name</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    placeholder="e.g., 30 hari full garansi"
                    value={field.state.value ? field.state.value.toLocaleString('id-ID') : ''}
                    onBlur={field.handleBlur}
                    onChange={(e) => {
                      const rawValue = e.target.value.replace(/[,.]/g, '');
                      const numericValue = rawValue ? parseInt(rawValue, 10) : 0;
                      field.handleChange(numericValue);
                    }}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="price">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Price</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="number"
                    step="0.01"
                    min="0"
                    placeholder="0.00"
                    value={field.state.value || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => {
                      const value = e.target.value;
                      field.handleChange(value ? parseFloat(value) : 0);
                    }}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <div>
            <form.Field name="description">
              {(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Description (Optional)</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    placeholder="Describe this variant..."
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    rows={3}
                  />
                  {field.state.meta.errors.map((error) => (
                    <p
                      key={error?.message}
                      className="text-sm text-destructive"
                    >
                      {error?.message}
                    </p>
                  ))}
                </div>
              )}
            </form.Field>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
            >
              Cancel
            </Button>
            <form.Subscribe>
              {(state) => (
                <Button
                  type="submit"
                  disabled={
                    !state.canSubmit ||
                    state.isSubmitting ||
                    updateVariantMutation.isPending
                  }
                >
                  {state.isSubmitting || updateVariantMutation.isPending
                    ? "Updating..."
                    : "Update Variant"}
                </Button>
              )}
            </form.Subscribe>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
