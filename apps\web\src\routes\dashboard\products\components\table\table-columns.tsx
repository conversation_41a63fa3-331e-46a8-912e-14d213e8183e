import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import {
  Package,
  EllipsisIcon,
  ExternalLinkIcon,
  EditIcon,
  TrashIcon,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useNavigate } from "@tanstack/react-router";
import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { trpcClient } from "@/utils/trpc";
import type { AppRouter } from "../../../../../../../server/src/routers";
import type { inferRouterOutputs } from "@trpc/server";

// Types
type RouterOutputs = inferRouterOutputs<AppRouter>;
type ProductsResponse = RouterOutputs["product"]["getAll"];
export type Product = ProductsResponse["products"][number];

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Product> = (row, columnId, filterValue) => {
  const searchableRowContent = `${row.original.name} ${
    row.original.description || ""
  } ${row.original.slug}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

// Row Actions Component
function RowActions({ row }: { row: any }) {
  const navigate = useNavigate();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const product = row.original as Product;

  const deleteProductMutation = useMutation({
    mutationFn: async (data: { id: string }) => {
      return trpcClient.product.delete.mutate({
        id: data.id,
      });
    },
    onSuccess: () => {
      toast.success("Product deleted successfully!");
      setDeleteDialogOpen(false);
      // Refresh the table data
      window.location.reload();
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete product");
    },
  });

  const handleViewDetails = () => {
    navigate({
      to: `/dashboard/products/${product.slug}`,
    });
  };

  const handleDeleteProduct = async () => {
    deleteProductMutation.mutate({ id: product.id });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <div className="flex justify-end">
            <Button
              size="icon"
              variant="ghost"
              className="shadow-none"
              aria-label="Product actions"
            >
              <EllipsisIcon size={16} aria-hidden="true" />
            </Button>
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={handleViewDetails}>
              <ExternalLinkIcon className="h-4 w-4 mr-2" />
              <span>View Details</span>
              <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleViewDetails}>
              <EditIcon className="h-4 w-4 mr-2" />
              <span>Edit Product</span>
              <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            className="text-destructive focus:text-destructive"
            onClick={() => setDeleteDialogOpen(true)}
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            <span>Delete Product</span>
            <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Product</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{product.name}"? This action
              cannot be undone. This will also delete all variants and stock
              items associated with this product.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProduct}
              disabled={deleteProductMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteProductMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

// Column Definitions
export const columns: ColumnDef<Product>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: "Product",
    accessorKey: "name",
    cell: ({ row }) => {
      const navigate = useNavigate();
      const product = row.original as Product;

      return (
        <div
          className="flex items-center gap-3 cursor-pointer hover:bg-muted/50 rounded-md p-1 -m-1 transition-colors"
          onClick={() =>
            navigate({ to: `/dashboard/products/${product.slug}` })
          }
        >
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
            <Package className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium hover:text-primary transition-colors">
              {row.getValue("name")}
            </div>
            <div className="text-sm text-muted-foreground">
              /{row.original.slug}
            </div>
          </div>
        </div>
      );
    },
    size: 250,
    filterFn: multiColumnFilterFn,
    enableHiding: false,
  },
  {
    header: "Description",
    accessorKey: "description",
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate"
        title={row.getValue("description") as string}
      >
        {row.getValue("description") || "—"}
      </div>
    ),
    size: 200,
  },
  {
    header: "Variants",
    accessorKey: "variants",
    cell: ({ row }) => {
      const variants = row.original.variants;
      return (
        <div className="flex items-center gap-2">
          <Badge variant="secondary">{variants.length}</Badge>
          {variants.length > 0 && (
            <span className="text-sm text-muted-foreground">
              $
              {Math.min(
                ...variants.map((v) => Number(v.price))
              ).toLocaleString()}{" "}
              - $
              {Math.max(
                ...variants.map((v) => Number(v.price))
              ).toLocaleString()}
            </span>
          )}
        </div>
      );
    },
    size: 150,
    enableSorting: false,
  },
  {
    header: "Stock",
    accessorKey: "stock",
    cell: ({ row }) => {
      const totalStock = row.original.variants.reduce(
        (acc: number, variant: any) => acc + variant.stock.length,
        0
      );
      const availableStock = row.original.variants.reduce(
        (acc: number, variant: any) =>
          acc +
          variant.stock.filter((s: any) => s.status === "AVAILABLE").length,
        0
      );

      return (
        <div className="flex items-center gap-2">
          <Badge
            variant={availableStock > 0 ? "default" : "secondary"}
            className={cn(
              availableStock === 0 &&
                "bg-muted-foreground/60 text-primary-foreground"
            )}
          >
            {availableStock}/{totalStock}
          </Badge>
          <span className="text-sm text-muted-foreground">available</span>
        </div>
      );
    },
    size: 120,
    enableSorting: false,
  },
  {
    header: "Created",
    accessorKey: "createdAt",
    cell: ({ row }) => {
      const dateValue = row.getValue("createdAt");
      const date = new Date(dateValue as string);
      return <div className="text-sm">{date.toLocaleDateString()}</div>;
    },
    size: 100,
  },
  {
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => <RowActions row={row} />,
    size: 60,
    enableHiding: false,
  },
];
